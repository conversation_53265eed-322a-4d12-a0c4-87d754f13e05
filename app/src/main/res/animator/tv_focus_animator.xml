<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 获得焦点时的动画 -->
    <item android:state_focused="true">
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:duration="200"
                android:valueTo="1.05"
                android:valueType="floatType"
                android:interpolator="@android:anim/decelerate_interpolator" />
            <objectAnimator
                android:propertyName="scaleY"
                android:duration="200"
                android:valueTo="1.05"
                android:valueType="floatType"
                android:interpolator="@android:anim/decelerate_interpolator" />
        </set>
    </item>
    
    <!-- 失去焦点时的动画 -->
    <item>
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:duration="200"
                android:valueTo="1.0"
                android:valueType="floatType"
                android:interpolator="@android:anim/accelerate_interpolator" />
            <objectAnimator
                android:propertyName="scaleY"
                android:duration="200"
                android:valueTo="1.0"
                android:valueType="floatType"
                android:interpolator="@android:anim/accelerate_interpolator" />
        </set>
    </item>
    
</selector>

