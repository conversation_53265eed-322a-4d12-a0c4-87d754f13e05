<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 外层白色发光效果 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="12dp" />
            <stroke
                android:width="3dp"
                android:color="#60FFFFFF" />
        </shape>
    </item>
    
    <!-- 主要白色边框 -->
    <item android:inset="1dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="11dp" />
            <stroke
                android:width="2dp"
                android:color="#FFFFFF" />
        </shape>
    </item>
    
</layer-list>

