<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 获得焦点时的状态 -->
    <item android:state_focused="true">
        <layer-list>
            <!-- 背景放大效果 -->
            <item android:drawable="@drawable/tv_remote_focus_effect" />
        </layer-list>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    
</selector>

