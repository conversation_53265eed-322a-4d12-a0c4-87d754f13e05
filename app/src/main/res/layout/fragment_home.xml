<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 动态背景：聚焦项目的 backdrop 图 -->
    <ImageView
        android:id="@+id/iv_backdrop"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:alpha="0.0"
        android:visibility="gone" />

    <!-- 顶部到内容的渐变遮罩，保证文字可读性 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/backdrop_gradient"
        android:visibility="gone" />

    <!-- 内容层 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:paddingTop="12dp"
            android:paddingBottom="24dp">

            <!-- 现代化顶部导航栏 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingVertical="12dp"
                android:layout_marginBottom="24dp">

                <!-- 左侧Logo和标题 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_play"
                        android:tint="@color/accent_color"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="TV Player"
                        android:textSize="20sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- 右侧设置按钮 -->
                <ImageView
                    android:id="@+id/iv_settings"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_settings"
                    android:tint="@color/text_secondary"
                    android:background="@drawable/circle_background"
                    android:padding="4dp"
                    android:focusable="true"
                    android:clickable="true"
                    android:background="@drawable/tv_remote_focus_selector"
                    android:stateListAnimator="@animator/tv_focus_animator"
                    android:contentDescription="设置" />

            </LinearLayout>



            <!-- 播放历史标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="播放历史"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_all_history"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="全部"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:focusable="true"
                    android:clickable="true"
                    android:background="@drawable/tv_remote_focus_selector"
                    android:stateListAnimator="@animator/tv_focus_animator"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_playback_history"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="24dp"
                android:nestedScrollingEnabled="false" />

            <!-- 电影标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="电影"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_movies"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="24dp"
                android:nestedScrollingEnabled="false" />

            <!-- 电视剧标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="电视剧"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_tv_shows"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="24dp"
                android:nestedScrollingEnabled="false"
                android:layout_marginBottom="24dp" />

        </LinearLayout>
    </ScrollView>

</FrameLayout>
